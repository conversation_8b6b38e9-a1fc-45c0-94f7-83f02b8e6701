import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ImageSourcePropType,
} from 'react-native';
import { styled } from 'nativewind';

// Card Action 的属性接口
export interface M3ECardActionProps {
  /** 按钮文本 */
  label: string;
  /** 按钮类型 */
  variant?: 'filled' | 'outlined' | 'text';
  /** 点击事件 */
  onPress?: () => void;
  /** 是否禁用 */
  disabled?: boolean;
}

// Card Header 的属性接口
export interface M3ECardHeaderProps {
  /** 头像组件或图片源 */
  avatar?: React.ReactNode | ImageSourcePropType;
  /** 头部标题 */
  title?: string;
  /** 头部副标题 */
  subtitle?: string;
  /** 右侧操作按钮 */
  action?: React.ReactNode;
}

// Card 的属性接口
export interface M3ECardProps {
  /** 卡片样式 */
  variant?: 'elevated' | 'filled' | 'outlined';
  /** 卡片头部 */
  header?: M3ECardHeaderProps;
  /** 媒体内容 */
  media?: {
    source: ImageSourcePropType;
    aspectRatio?: number;
    height?: number;
  };
  /** 卡片标题 */
  title?: string;
  /** 卡片副标题 */
  subtitle?: string;
  /** 支持文本 */
  supportingText?: string;
  /** 主要操作按钮 */
  primaryAction?: M3ECardActionProps;
  /** 次要操作按钮 */
  secondaryAction?: M3ECardActionProps;
  /** 自定义内容 */
  children?: React.ReactNode;
  /** 点击事件 */
  onPress?: () => void;
  /** 自定义样式类名 */
  className?: string;
}

// 样式化的卡片容器
const StyledCardContainer = styled(TouchableOpacity, {
  props: {
    variant: true,
    pressable: true,
  },
  variants: {
    variant: {
      elevated: 'bg-surface-container-low shadow-md',
      filled: 'bg-surface-container-highest',
      outlined: 'bg-surface border border-outline',
    },
    pressable: {
      true: 'active:opacity-80',
      false: '',
    },
  },
  defaultVariants: {
    variant: 'elevated',
    pressable: false,
  },
});

// 样式化的操作按钮
const StyledActionButton = styled(TouchableOpacity, {
  props: {
    variant: true,
    disabled: true,
  },
  variants: {
    variant: {
      filled: 'bg-primary rounded-full px-4 py-2.5',
      outlined: 'border border-outline rounded-full px-4 py-2.5',
      text: 'px-4 py-2.5',
    },
    disabled: {
      true: 'opacity-50',
      false: '',
    },
  },
  defaultVariants: {
    variant: 'filled',
    disabled: false,
  },
});

/**
 * Card Action 组件
 */
const CardAction: React.FC<M3ECardActionProps> = ({
  label,
  variant = 'filled',
  onPress,
  disabled = false,
}) => {
  const textColorClass =
    variant === 'filled' ? 'text-on-primary' : 'text-primary';

  return (
    <StyledActionButton
      variant={variant}
      disabled={disabled}
      onPress={disabled ? undefined : onPress}
      activeOpacity={0.7}
      className="h-10"
    >
      <Text className={`text-sm font-medium ${textColorClass}`}>{label}</Text>
    </StyledActionButton>
  );
};

/**
 * Card Header 组件
 */
const CardHeader: React.FC<M3ECardHeaderProps> = ({
  avatar,
  title,
  subtitle,
  action,
}) => {
  return (
    <View className="flex-row items-center px-4 py-3">
      <View className="flex-row items-center flex-1 gap-4">
        {avatar && (
          <View className="w-10 h-10 rounded-full overflow-hidden bg-primary-container items-center justify-center">
            {React.isValidElement(avatar) ? (
              avatar
            ) : (
              <Image
                source={avatar as ImageSourcePropType}
                className="w-full h-full"
                resizeMode="cover"
              />
            )}
          </View>
        )}

        {(title || subtitle) && (
          <View className="flex-1">
            {title && (
              <Text className="text-base font-medium text-on-surface">
                {title}
              </Text>
            )}
            {subtitle && (
              <Text className="text-sm text-on-surface-variant">
                {subtitle}
              </Text>
            )}
          </View>
        )}
      </View>

      {action && <View>{action}</View>}
    </View>
  );
};

/**
 * M3E Card 组件
 *
 * 基于 Material Design 3 规范的卡片组件，用于显示内容和操作。
 *
 * @example
 * ```tsx
 * <M3ECard
 *   variant="elevated"
 *   header={{
 *     avatar: <Icon name="person" />,
 *     title: "用户名",
 *     subtitle: "副标题",
 *     action: <IconButton icon="more-vert" />
 *   }}
 *   media={{
 *     source: { uri: 'https://example.com/image.jpg' },
 *     height: 200
 *   }}
 *   title="卡片标题"
 *   supportingText="这是支持文本，提供更多信息..."
 *   primaryAction={{
 *     label: "主要操作",
 *     onPress: () => console.log('Primary action')
 *   }}
 *   secondaryAction={{
 *     label: "次要操作",
 *     variant: "outlined",
 *     onPress: () => console.log('Secondary action')
 *   }}
 * />
 * ```
 */
export const M3ECard: React.FC<M3ECardProps> = ({
  variant = 'elevated',
  header,
  media,
  title,
  subtitle,
  supportingText,
  primaryAction,
  secondaryAction,
  children,
  onPress,
  className = '',
}) => {
  const baseClasses = 'rounded-xl overflow-hidden';
  const combinedClasses = `${baseClasses} ${className}`;

  const isPressable = !!onPress;

  return (
    <StyledCardContainer
      variant={variant}
      pressable={isPressable}
      onPress={onPress}
      activeOpacity={isPressable ? 0.8 : 1}
      className={combinedClasses}
    >
      {/* Header */}
      {header && <CardHeader {...header} />}

      {/* Media */}
      {media && (
        <View className="w-full" style={{ height: media.height || 200 }}>
          <Image
            source={media.source}
            className="w-full h-full"
            resizeMode="cover"
            style={{ aspectRatio: media.aspectRatio }}
          />
        </View>
      )}

      {/* Content */}
      {(title || subtitle || supportingText || children) && (
        <View className="px-4 py-4 gap-2">
          {title && (
            <Text className="text-lg font-normal text-on-surface">{title}</Text>
          )}

          {subtitle && (
            <Text className="text-sm text-on-surface-variant">{subtitle}</Text>
          )}

          {supportingText && (
            <Text className="text-sm text-on-surface-variant leading-5">
              {supportingText}
            </Text>
          )}

          {children}
        </View>
      )}

      {/* Actions */}
      {(primaryAction || secondaryAction) && (
        <View className="flex-row justify-end items-center gap-2 px-4 pb-4">
          {secondaryAction && <CardAction {...secondaryAction} />}
          {primaryAction && <CardAction {...primaryAction} />}
        </View>
      )}
    </StyledCardContainer>
  );
};

export default M3ECard;
