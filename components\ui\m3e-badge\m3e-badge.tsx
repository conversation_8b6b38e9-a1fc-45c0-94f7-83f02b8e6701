import React from 'react';
import { View, Text } from 'react-native';
import { styled } from 'nativewind';

// Badge 组件的属性接口
export interface M3EBadgeProps {
  /** 徽章显示的文本或数字 */
  label?: string | number;
  /** 徽章大小 */
  size?: 'small' | 'large';
  /** 是否显示徽章 */
  visible?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 子组件 */
  children?: React.ReactNode;
}

// 样式化的容器组件
const StyledBadgeContainer = styled(View, {
  props: {
    size: true,
    visible: true,
  },
  variants: {
    size: {
      small: 'min-w-[6px] h-[6px] rounded-full',
      large: 'min-w-[16px] h-[16px] rounded-full px-1',
    },
    visible: {
      true: 'opacity-100',
      false: 'opacity-0',
    },
  },
  defaultVariants: {
    size: 'large',
    visible: true,
  },
});

// 样式化的文本组件
const StyledBadgeText = styled(Text, {
  props: {
    size: true,
  },
  variants: {
    size: {
      small: 'text-[8px] font-medium leading-none',
      large: 'text-[11px] font-medium leading-none tracking-[0.5px]',
    },
  },
  defaultVariants: {
    size: 'large',
  },
});

/**
 * M3E Badge 组件
 * 
 * 基于 Material Design 3 规范的徽章组件，用于显示通知、计数或其他信息。
 * 
 * @example
 * ```tsx
 * // 基础用法
 * <M3EBadge label="3" />
 * 
 * // 小尺寸徽章
 * <M3EBadge size="small" />
 * 
 * // 带有图标的徽章
 * <View className="relative">
 *   <Icon name="notifications" />
 *   <M3EBadge 
 *     label="5" 
 *     className="absolute -top-1 -right-1" 
 *   />
 * </View>
 * ```
 */
export const M3EBadge: React.FC<M3EBadgeProps> = ({
  label,
  size = 'large',
  visible = true,
  className = '',
  children,
}) => {
  // 如果有 label 且 size 为 small，则使用 large 尺寸
  const actualSize = label && size === 'small' ? 'large' : size;
  
  // 基础样式类
  const baseClasses = 'bg-red-600 items-center justify-center';
  
  // 组合样式类
  const combinedClasses = `${baseClasses} ${className}`;

  return (
    <StyledBadgeContainer
      size={actualSize}
      visible={visible}
      className={combinedClasses}
    >
      {label && (
        <StyledBadgeText
          size={actualSize}
          className="text-white text-center"
        >
          {label}
        </StyledBadgeText>
      )}
      {children}
    </StyledBadgeContainer>
  );
};

export default M3EBadge;
