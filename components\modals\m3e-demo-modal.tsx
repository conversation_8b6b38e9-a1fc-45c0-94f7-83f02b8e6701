import React, { useState } from 'react';
import { ScrollView, View, Text, StyleSheet, Modal, Pressable } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/use-app-theme';
import {
  M3EButton,
  M3EButtonGroups,
  M3EFAB,
  M3EFABMenu,
  M3EIconButton,
  M3ESplitButton,
  type M3EFABMenuItem,
} from '@/components/ui/m3e-button';
import { Ionicons } from '@expo/vector-icons';

interface M3EDemoModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function M3EDemoModal({ visible, onClose }: M3EDemoModalProps) {
  const theme = useAppTheme();
  const [selectedGroupIndex, setSelectedGroupIndex] = useState(0);
  const [isFABMenuOpen, setIsFABMenuOpen] = useState(false);

  const isDark = theme.dark;

  const fabMenuItems: M3EFABMenuItem[] = [
    {
      icon: (
        <Ionicons name="add" size={24} color={isDark ? '#4F378A' : '#4F378A'} />
      ),
      label: 'Create',
      onPress: () => console.log('Create pressed'),
    },
    {
      icon: (
        <Ionicons
          name="edit"
          size={24}
          color={isDark ? '#4F378A' : '#4F378A'}
        />
      ),
      label: 'Edit',
      onPress: () => console.log('Edit pressed'),
    },
    {
      icon: (
        <Ionicons
          name="share"
          size={24}
          color={isDark ? '#4F378A' : '#4F378A'}
        />
      ),
      label: 'Share',
      onPress: () => console.log('Share pressed'),
    },
  ];

  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContainer: {
      width: '90%',
      maxWidth: 600,
      height: '80%',
      backgroundColor: isDark ? '#1D1B20' : '#FEF7FF',
      borderRadius: 16,
      overflow: 'hidden',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: isDark ? '#49454F' : '#E6E0E9',
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: isDark ? '#E6E0E9' : '#1D1B20',
    },
    closeButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: isDark ? '#49454F' : '#E6E0E9',
    },
    scrollContent: {
      padding: 16,
    },
    section: {
      marginBottom: 32,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: isDark ? '#E6E0E9' : '#1D1B20',
      marginBottom: 16,
    },
    subsectionTitle: {
      fontSize: 16,
      fontWeight: '500',
      color: isDark ? '#E6E0E9' : '#1D1B20',
      marginBottom: 12,
      marginTop: 16,
    },
    buttonRow: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
      marginBottom: 16,
    },
    fabContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 16,
      marginBottom: 16,
    },
    fabMenuContainer: {
      position: 'relative',
      alignItems: 'center',
      justifyContent: 'center',
      height: 200,
      marginBottom: 16,
    },
  });

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>M3E 组件演示</Text>
            <Pressable style={styles.closeButton} onPress={onClose}>
              <Ionicons 
                name="close" 
                size={20} 
                color={isDark ? '#E6E0E9' : '#1D1B20'} 
              />
            </Pressable>
          </View>

          {/* Content */}
          <ScrollView style={styles.scrollContent}>
            {/* M3E Buttons */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Buttons</Text>

              <Text style={styles.subsectionTitle}>Filled Buttons</Text>
              <View style={styles.buttonRow}>
                <M3EButton
                  variant="filled"
                  size="small"
                  onPress={() => console.log('Small filled pressed')}
                >
                  Small
                </M3EButton>
                <M3EButton
                  variant="filled"
                  size="medium"
                  onPress={() => console.log('Medium filled pressed')}
                >
                  Medium
                </M3EButton>
                <M3EButton
                  variant="filled"
                  size="large"
                  onPress={() => console.log('Large filled pressed')}
                >
                  Large
                </M3EButton>
              </View>

              <Text style={styles.subsectionTitle}>Outlined Buttons</Text>
              <View style={styles.buttonRow}>
                <M3EButton
                  variant="outlined"
                  size="small"
                  onPress={() => console.log('Small outlined pressed')}
                >
                  Small
                </M3EButton>
                <M3EButton
                  variant="outlined"
                  size="medium"
                  onPress={() => console.log('Medium outlined pressed')}
                >
                  Medium
                </M3EButton>
                <M3EButton
                  variant="outlined"
                  size="large"
                  onPress={() => console.log('Large outlined pressed')}
                >
                  Large
                </M3EButton>
              </View>

              <Text style={styles.subsectionTitle}>Text Buttons</Text>
              <View style={styles.buttonRow}>
                <M3EButton
                  variant="text"
                  size="small"
                  onPress={() => console.log('Small text pressed')}
                >
                  Small
                </M3EButton>
                <M3EButton
                  variant="text"
                  size="medium"
                  onPress={() => console.log('Medium text pressed')}
                >
                  Medium
                </M3EButton>
                <M3EButton
                  variant="text"
                  size="large"
                  onPress={() => console.log('Large text pressed')}
                >
                  Large
                </M3EButton>
              </View>

              <Text style={styles.subsectionTitle}>With Icons</Text>
              <View style={styles.buttonRow}>
                <M3EButton
                  variant="filled"
                  size="medium"
                  icon="add"
                  onPress={() => console.log('Icon button pressed')}
                >
                  Add Item
                </M3EButton>
                <M3EButton
                  variant="outlined"
                  size="medium"
                  icon="download"
                  onPress={() => console.log('Download pressed')}
                >
                  Download
                </M3EButton>
              </View>
            </View>

            {/* M3E Button Groups */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Button Groups</Text>

              <Text style={styles.subsectionTitle}>Round Button Groups</Text>
              <View style={styles.buttonRow}>
                <M3EButtonGroups
                  type="round"
                  size="medium"
                  slots={3}
                  labels={['First', 'Second', 'Third']}
                  selectedIndex={selectedGroupIndex}
                  onPress={setSelectedGroupIndex}
                />
              </View>

              <Text style={styles.subsectionTitle}>Square Button Groups</Text>
              <View style={styles.buttonRow}>
                <M3EButtonGroups
                  type="square"
                  size="medium"
                  slots={4}
                  labels={['Option A', 'Option B', 'Option C', 'Option D']}
                  selectedIndex={0}
                  onPress={(index) => console.log('Square group pressed:', index)}
                />
              </View>
            </View>

            {/* M3E FAB */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E FAB</Text>

              <Text style={styles.subsectionTitle}>Standard FABs</Text>
              <View style={styles.fabContainer}>
                <M3EFAB
                  size="small"
                  variant="primary"
                  icon={<Ionicons name="add" size={18} color="#FFFFFF" />}
                  onPress={() => console.log('Small FAB pressed')}
                />
                <M3EFAB
                  size="medium"
                  variant="primary"
                  icon={<Ionicons name="add" size={24} color="#FFFFFF" />}
                  onPress={() => console.log('Medium FAB pressed')}
                />
                <M3EFAB
                  size="large"
                  variant="primary"
                  icon={<Ionicons name="add" size={36} color="#FFFFFF" />}
                  onPress={() => console.log('Large FAB pressed')}
                />
              </View>
            </View>

            {/* M3E Icon Button */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Icon Button</Text>

              <Text style={styles.subsectionTitle}>Standard Icon Buttons</Text>
              <View style={styles.buttonRow}>
                <M3EIconButton
                  variant="standard"
                  size="small"
                  icon={
                    <Ionicons
                      name="heart-outline"
                      size={20}
                      color={isDark ? '#CAC4D0' : '#49454F'}
                    />
                  }
                  onPress={() => console.log('Standard small pressed')}
                />
                <M3EIconButton
                  variant="standard"
                  size="medium"
                  icon={
                    <Ionicons
                      name="heart-outline"
                      size={24}
                      color={isDark ? '#CAC4D0' : '#49454F'}
                    />
                  }
                  onPress={() => console.log('Standard medium pressed')}
                />
              </View>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}
